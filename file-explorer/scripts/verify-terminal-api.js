#!/usr/bin/env node

/**
 * Terminal API Verification Script
 *
 * This script verifies that the Terminal API is properly set up
 * by checking file existence, configuration, and code structure.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Terminal API Implementation...\n');

const checks = [];

// Check 1: Preload script exists and contains terminalAPI
function checkPreloadScript() {
  const preloadPath = path.join(__dirname, '../electron/preload.js');
  const distPreloadPath = path.join(__dirname, '../dist-electron/preload.js');

  if (!fs.existsSync(preloadPath)) {
    return { pass: false, message: 'Source preload.js not found' };
  }

  if (!fs.existsSync(distPreloadPath)) {
    return { pass: false, message: 'Compiled preload.js not found in dist-electron' };
  }

  const preloadContent = fs.readFileSync(distPreloadPath, 'utf8');

  if (!preloadContent.includes('terminalAPI')) {
    return { pass: false, message: 'terminalAPI not found in preload script' };
  }

  if (!preloadContent.includes('contextBridge.exposeInMainWorld')) {
    return { pass: false, message: 'contextBridge not used in preload script' };
  }

  const requiredMethods = [
    'startTerminal',
    'writeToTerminal',
    'resizeTerminal',
    'closeTerminal',
    'onTerminalData',
    'onTerminalExit'
  ];

  const missingMethods = requiredMethods.filter(method => !preloadContent.includes(method));

  if (missingMethods.length > 0) {
    return { pass: false, message: `Missing methods: ${missingMethods.join(', ')}` };
  }

  return { pass: true, message: 'Preload script properly configured' };
}

// Check 2: Main process has IPC handlers
function checkMainProcessHandlers() {
  const mainPath = path.join(__dirname, '../electron/main.ts');
  const distMainPath = path.join(__dirname, '../dist-electron/main.js');

  if (!fs.existsSync(mainPath)) {
    return { pass: false, message: 'main.ts not found' };
  }

  if (!fs.existsSync(distMainPath)) {
    return { pass: false, message: 'Compiled main.js not found' };
  }

  const mainContent = fs.readFileSync(distMainPath, 'utf8');

  const requiredHandlers = [
    'terminal:start',
    'terminal:input',
    'terminal:resize',
    'terminal:close'
  ];

  const missingHandlers = requiredHandlers.filter(handler => !mainContent.includes(handler));

  if (missingHandlers.length > 0) {
    return { pass: false, message: `Missing IPC handlers: ${missingHandlers.join(', ')}` };
  }

  return { pass: true, message: 'All IPC handlers present' };
}

// Check 3: BrowserWindow configuration
function checkBrowserWindowConfig() {
  const mainPath = path.join(__dirname, '../dist-electron/main.js');

  if (!fs.existsSync(mainPath)) {
    return { pass: false, message: 'main.js not found' };
  }

  const mainContent = fs.readFileSync(mainPath, 'utf8');

  if (!mainContent.includes('preload.js')) {
    return { pass: false, message: 'Preload script not configured in BrowserWindow' };
  }

  if (!mainContent.includes('contextIsolation: true')) {
    return { pass: false, message: 'Context isolation not enabled' };
  }

  if (!mainContent.includes('nodeIntegration: false')) {
    return { pass: false, message: 'Node integration not properly disabled' };
  }

  return { pass: true, message: 'BrowserWindow properly configured' };
}

// Check 4: TerminalPanel component
function checkTerminalPanel() {
  const terminalPanelPath = path.join(__dirname, '../components/terminal/TerminalPanel.tsx');

  if (!fs.existsSync(terminalPanelPath)) {
    return { pass: false, message: 'TerminalPanel.tsx not found' };
  }

  const panelContent = fs.readFileSync(terminalPanelPath, 'utf8');

  if (!panelContent.includes('electronAPI?.terminalAPI')) {
    return { pass: false, message: 'TerminalPanel does not check for terminalAPI' };
  }

  if (!panelContent.includes('Terminal API not available')) {
    return { pass: false, message: 'TerminalPanel missing error handling' };
  }

  return { pass: true, message: 'TerminalPanel properly implemented' };
}

// Check 5: Package dependencies
function checkDependencies() {
  const packagePath = path.join(__dirname, '../package.json');

  if (!fs.existsSync(packagePath)) {
    return { pass: false, message: 'package.json not found' };
  }

  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

  const requiredDeps = ['electron', 'node-pty', 'xterm', 'xterm-addon-fit'];
  const missingDeps = requiredDeps.filter(dep =>
    !packageContent.dependencies[dep] && !packageContent.devDependencies[dep]
  );

  if (missingDeps.length > 0) {
    return { pass: false, message: `Missing dependencies: ${missingDeps.join(', ')}` };
  }

  return { pass: true, message: 'All required dependencies present' };
}

// Run all checks
const allChecks = [
  { name: 'Preload Script', check: checkPreloadScript },
  { name: 'IPC Handlers', check: checkMainProcessHandlers },
  { name: 'BrowserWindow Config', check: checkBrowserWindowConfig },
  { name: 'TerminalPanel Component', check: checkTerminalPanel },
  { name: 'Dependencies', check: checkDependencies }
];

let allPassed = true;

allChecks.forEach(({ name, check }) => {
  const result = check();
  const status = result.pass ? '✅' : '❌';
  console.log(`${status} ${name}: ${result.message}`);

  if (!result.pass) {
    allPassed = false;
  }
});

console.log('\n' + '='.repeat(50));
console.log(`Overall Status: ${allPassed ? '✅ PASS' : '❌ FAIL'}`);

if (allPassed) {
  console.log('\n🎉 Terminal API is properly configured!');
  console.log('\nTo test the implementation:');
  console.log('1. Run: npm run electron:dev');
  console.log('2. Open DevTools in the Electron window');
  console.log('3. Check: typeof window.electronAPI.terminalAPI');
  console.log('4. Navigate to a terminal panel and test functionality');
} else {
  console.log('\n🔧 Please fix the issues above and run this script again.');
}

process.exit(allPassed ? 0 : 1);
