#!/usr/bin/env node

/**
 * ✅ Terminal API Diagnostic Script
 * Comprehensive testing of Electron preload script and API availability
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔍 Terminal API Diagnostic Tool');
console.log('================================\n');

// Check 1: Verify files exist
console.log('📁 Checking required files:');
const requiredFiles = [
  'dist-electron/main.js',
  'dist-electron/preload.js',
  'package.json'
];

for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING!`);
    process.exit(1);
  }
}

// Check 2: Verify preload script content
console.log('\n🔍 Checking preload script content:');
const preloadContent = fs.readFileSync('dist-electron/preload.js', 'utf8');

const requiredAPIs = [
  'terminalAPI',
  'startTerminal',
  'writeToTerminal',
  'onTerminalData'
];

for (const api of requiredAPIs) {
  if (preloadContent.includes(api)) {
    console.log(`  ✅ ${api} found in preload script`);
  } else {
    console.log(`  ❌ ${api} NOT found in preload script`);
  }
}

// Check 3: Test Electron startup with detailed logging
console.log('\n🚀 Testing Electron with enhanced logging:');
console.log('   (Will run for 10 seconds to capture console output)\n');

const electronPath = require('electron');
const electronProcess = spawn(electronPath, ['.'], {
  stdio: ['pipe', 'pipe', 'pipe'],
  env: {
    ...process.env,
    ELECTRON_ENABLE_LOGGING: '1',
    DEBUG: '*'
  }
});

let stdoutData = '';
let stderrData = '';

electronProcess.stdout.on('data', (data) => {
  const output = data.toString();
  stdoutData += output;
  
  // Real-time output for important messages
  if (output.includes('[Preload]') || output.includes('Terminal') || output.includes('API') || output.includes('Debug info')) {
    console.log(`  📤 STDOUT: ${output.trim()}`);
  }
});

electronProcess.stderr.on('data', (data) => {
  const output = data.toString();
  stderrData += output;
  
  // Show errors immediately
  if (output.includes('Error') || output.includes('error')) {
    console.log(`  📥 STDERR: ${output.trim()}`);
  }
});

// Kill after 10 seconds
setTimeout(() => {
  console.log('\n⏰ Stopping Electron after 10 seconds...\n');
  electronProcess.kill('SIGTERM');
  
  // Wait a bit for cleanup
  setTimeout(() => {
    console.log('📊 Analysis Results:');
    console.log('==================\n');
    
    // Analyze stdout
    console.log('🔍 Preload Script Analysis:');
    if (stdoutData.includes('Electron API exposed to renderer process')) {
      console.log('  ✅ Preload script executed successfully');
    } else {
      console.log('  ❌ Preload script did NOT execute');
    }
    
    if (stdoutData.includes('Terminal API methods available')) {
      console.log('  ✅ Terminal API was exposed');
    } else {
      console.log('  ❌ Terminal API was NOT exposed');
    }
    
    if (stdoutData.includes('electronAPI is available on window')) {
      console.log('  ✅ API verification passed');
    } else {
      console.log('  ❌ API verification failed');
    }
    
    console.log('\n🔍 Terminal Component Analysis:');
    if (stdoutData.includes('Terminal API detected and set')) {
      console.log('  ✅ React component detected API');
    } else if (stdoutData.includes('API detection retry')) {
      console.log('  ⚠️  React component is retrying API detection');
    } else {
      console.log('  ❌ React component did not detect API');
    }
    
    console.log('\n🔍 Backend Analysis:');
    if (stdoutData.includes('node-pty loaded successfully')) {
      console.log('  ✅ node-pty backend loaded');
    } else {
      console.log('  ❌ node-pty backend failed to load');
    }
    
    // Show any errors
    if (stderrData.trim()) {
      console.log('\n⚠️  Errors detected:');
      console.log(stderrData);
    }
    
    console.log('\n📋 Recommendations:');
    if (!stdoutData.includes('Electron API exposed to renderer process')) {
      console.log('  🔧 Preload script not executing - check path in main.ts');
    }
    if (!stdoutData.includes('electronAPI is available on window')) {
      console.log('  🔧 API not available on window - timing issue or context isolation problem');
    }
    if (!stdoutData.includes('Terminal API detected and set')) {
      console.log('  🔧 React component cannot access API - check component mounting timing');
    }
    
    process.exit(0);
  }, 1000);
}, 10000);

electronProcess.on('error', (error) => {
  console.error('❌ Failed to start Electron:', error);
  process.exit(1);
});
