#!/usr/bin/env node

/**
 * Terminal Issue Diagnostic Script
 * 
 * This script helps diagnose common terminal API issues and provides
 * specific solutions based on the detected problems.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Terminal API Diagnostic Tool\n');

const issues = [];
const solutions = [];

// Check 1: Verify Electron is installed
function checkElectronInstallation() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    if (!packageJson.devDependencies?.electron && !packageJson.dependencies?.electron) {
      issues.push('❌ Electron not found in package.json');
      solutions.push('Install Electron: npm install --save-dev electron');
      return false;
    }
    console.log('✅ Electron is installed');
    return true;
  } catch (error) {
    issues.push('❌ Cannot read package.json');
    solutions.push('Ensure you are in the correct project directory');
    return false;
  }
}

// Check 2: Verify preload script exists and is compiled
function checkPreloadScript() {
  const sourcePreload = 'electron/preload.js';
  const compiledPreload = 'dist-electron/preload.js';
  
  if (!fs.existsSync(sourcePreload)) {
    issues.push('❌ Source preload script not found: electron/preload.js');
    solutions.push('Create the preload script or check file path');
    return false;
  }
  
  if (!fs.existsSync(compiledPreload)) {
    issues.push('❌ Compiled preload script not found: dist-electron/preload.js');
    solutions.push('Run: npm run copy:preload');
    return false;
  }
  
  // Check if terminalAPI is in the preload script
  const preloadContent = fs.readFileSync(compiledPreload, 'utf8');
  if (!preloadContent.includes('terminalAPI')) {
    issues.push('❌ terminalAPI not found in preload script');
    solutions.push('Update preload script to include terminalAPI');
    return false;
  }
  
  console.log('✅ Preload script exists and contains terminalAPI');
  return true;
}

// Check 3: Verify main.ts is compiled
function checkMainScript() {
  const sourceMain = 'electron/main.ts';
  const compiledMain = 'dist-electron/main.js';
  
  if (!fs.existsSync(sourceMain)) {
    issues.push('❌ Source main script not found: electron/main.ts');
    solutions.push('Create the main script or check file path');
    return false;
  }
  
  if (!fs.existsSync(compiledMain)) {
    issues.push('❌ Compiled main script not found: dist-electron/main.js');
    solutions.push('Run: npm run compile:electron');
    return false;
  }
  
  // Check if terminal IPC handlers are present
  const mainContent = fs.readFileSync(compiledMain, 'utf8');
  const requiredHandlers = ['terminal:start', 'terminal:input', 'terminal:resize', 'terminal:close'];
  const missingHandlers = requiredHandlers.filter(handler => !mainContent.includes(handler));
  
  if (missingHandlers.length > 0) {
    issues.push(`❌ Missing IPC handlers: ${missingHandlers.join(', ')}`);
    solutions.push('Update main.ts to include all terminal IPC handlers');
    return false;
  }
  
  console.log('✅ Main script compiled and contains terminal IPC handlers');
  return true;
}

// Check 4: Verify node-pty is installed
function checkNodePty() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    if (!packageJson.dependencies?.['node-pty-prebuilt-multiarch']) {
      issues.push('❌ node-pty-prebuilt-multiarch not found in dependencies');
      solutions.push('Install node-pty-prebuilt-multiarch: npm install node-pty-prebuilt-multiarch');
      return false;
    }

    // Try to require node-pty-prebuilt-multiarch to check if it's properly compiled
    try {
      require('node-pty-prebuilt-multiarch');
      console.log('✅ node-pty-prebuilt-multiarch is installed and accessible');
      return true;
    } catch (error) {
      issues.push('❌ node-pty-prebuilt-multiarch installed but not accessible');
      solutions.push('Reinstall node-pty-prebuilt-multiarch: npm install node-pty-prebuilt-multiarch --force');
      return false;
    }
  } catch (error) {
    issues.push('❌ Cannot check node-pty-prebuilt-multiarch installation');
    return false;
  }
}

// Check 5: Verify correct scripts in package.json
function checkPackageScripts() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredScripts = [
      'electron:dev',
      'compile:electron',
      'copy:preload'
    ];
    
    const missingScripts = requiredScripts.filter(script => !packageJson.scripts?.[script]);
    
    if (missingScripts.length > 0) {
      issues.push(`❌ Missing package.json scripts: ${missingScripts.join(', ')}`);
      solutions.push('Add missing scripts to package.json');
      return false;
    }
    
    console.log('✅ All required scripts present in package.json');
    return true;
  } catch (error) {
    issues.push('❌ Cannot read package.json scripts');
    return false;
  }
}

// Check 6: Verify development server is not running on terminal port
function checkPortConflicts() {
  try {
    // Check if Next.js dev server is running
    const result = execSync('lsof -ti:4444 2>/dev/null || echo "none"', { encoding: 'utf8' }).trim();
    if (result !== 'none') {
      console.log('ℹ️ Next.js dev server is running on port 4444');
      console.log('   This is normal for electron:dev mode');
    } else {
      console.log('ℹ️ No process running on port 4444');
    }
    return true;
  } catch (error) {
    console.log('ℹ️ Cannot check port status (this is normal)');
    return true;
  }
}

// Main diagnostic function
function runDiagnostics() {
  console.log('Running diagnostics...\n');
  
  const checks = [
    { name: 'Electron Installation', fn: checkElectronInstallation },
    { name: 'Preload Script', fn: checkPreloadScript },
    { name: 'Main Script', fn: checkMainScript },
    { name: 'node-pty Installation', fn: checkNodePty },
    { name: 'Package Scripts', fn: checkPackageScripts },
    { name: 'Port Conflicts', fn: checkPortConflicts }
  ];
  
  let allPassed = true;
  
  checks.forEach(({ name, fn }) => {
    try {
      const result = fn();
      if (!result) allPassed = false;
    } catch (error) {
      console.log(`❌ ${name}: Error during check - ${error.message}`);
      allPassed = false;
    }
  });
  
  console.log('\n' + '='.repeat(60));
  
  if (issues.length > 0) {
    console.log('🔧 Issues Found:');
    issues.forEach(issue => console.log(`  ${issue}`));
    
    console.log('\n💡 Recommended Solutions:');
    solutions.forEach(solution => console.log(`  • ${solution}`));
    
    console.log('\n📋 Quick Fix Commands:');
    console.log('  npm run compile:electron');
    console.log('  npm run copy:preload');
    console.log('  npm run electron:dev');
  } else {
    console.log('✅ All checks passed!');
  }
  
  console.log('\n🚀 To start the terminal:');
  console.log('  1. Stop any running "npm run dev" processes');
  console.log('  2. Run: npm run electron:dev');
  console.log('  3. Open terminal panel in the Electron app');
  
  console.log('\n🔍 If terminal still doesn\'t work:');
  console.log('  1. Open DevTools in Electron window');
  console.log('  2. Check console for "Electron API exposed" message');
  console.log('  3. Test: typeof window.electronAPI.terminalAPI');
  console.log('  4. Look for any error messages in console');
  
  return allPassed;
}

// Run the diagnostics
const success = runDiagnostics();
process.exit(success ? 0 : 1);
