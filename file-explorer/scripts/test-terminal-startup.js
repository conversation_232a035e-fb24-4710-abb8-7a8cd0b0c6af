#!/usr/bin/env node

/**
 * Task 86 - Terminal Startup Test Script
 *
 * This script tests the enhanced terminal startup functionality to ensure:
 * 1. PTY backend starts correctly with detailed logging
 * 2. Shell detection and fallback works properly
 * 3. Error handling and red banner display functions
 * 4. IPC communication is stable
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Terminal Startup Implementation...\n');

// Test 1: Check enhanced logging in main.ts
function testEnhancedLogging() {
  const mainPath = path.join(__dirname, '../dist-electron/main.js');

  if (!fs.existsSync(mainPath)) {
    return { pass: false, message: 'Compiled main.js not found' };
  }

  const mainContent = fs.readFileSync(mainPath, 'utf8');

  const requiredLogs = [
    '[Terminal:Start]',
    'PTY options:',
    'Unix: Using shell',
    'PTY process created successfully',
    'Session stored'
  ];

  const missingLogs = requiredLogs.filter(log => !mainContent.includes(log));

  if (missingLogs.length > 0) {
    return { pass: false, message: `Missing enhanced logging: ${missingLogs.join(', ')}` };
  }

  return { pass: true, message: 'Enhanced logging implemented' };
}

// Test 2: Check shell fallback logic
function testShellFallback() {
  const mainPath = path.join(__dirname, '../dist-electron/main.js');

  if (!fs.existsSync(mainPath)) {
    return { pass: false, message: 'Compiled main.js not found' };
  }

  const mainContent = fs.readFileSync(mainPath, 'utf8');

  const shellFeatures = [
    'process.env.SHELL',
    'shellFallbacks',
    '/bin/bash',
    '/bin/sh',
    'powershell.exe'
  ];

  const missingFeatures = shellFeatures.filter(feature => !mainContent.includes(feature));

  if (missingFeatures.length > 0) {
    return { pass: false, message: `Missing shell fallback features: ${missingFeatures.join(', ')}` };
  }

  return { pass: true, message: 'Shell fallback logic implemented' };
}

// Test 3: Check PTY error handling
function testPtyErrorHandling() {
  const mainPath = path.join(__dirname, '../dist-electron/main.js');

  if (!fs.existsSync(mainPath)) {
    return { pass: false, message: 'Compiled main.js not found' };
  }

  const mainContent = fs.readFileSync(mainPath, 'utf8');

  const errorFeatures = [
    'node-pty not loaded',
    'PTY process creation returned null',
    'Error details:',
    'platform: process.platform',
    'nodeVersion: process.version'
  ];

  const missingFeatures = errorFeatures.filter(feature => !mainContent.includes(feature));

  if (missingFeatures.length > 0) {
    return { pass: false, message: `Missing PTY error handling: ${missingFeatures.join(', ')}` };
  }

  return { pass: true, message: 'PTY error handling implemented' };
}

// Test 4: Check frontend error handling
function testFrontendErrorHandling() {
  const terminalPanelPath = path.join(__dirname, '../components/terminal/TerminalPanel.tsx');

  if (!fs.existsSync(terminalPanelPath)) {
    return { pass: false, message: 'TerminalPanel.tsx not found' };
  }

  const panelContent = fs.readFileSync(terminalPanelPath, 'utf8');

  const errorFeatures = [
    'ptyError',
    'setPtyError',
    'connectionAttempts',
    'PTY Backend Failed',
    'bg-red-500/10',
    'Retry PTY'
  ];

  const missingFeatures = errorFeatures.filter(feature => !panelContent.includes(feature));

  if (missingFeatures.length > 0) {
    return { pass: false, message: `Missing frontend error handling: ${missingFeatures.join(', ')}` };
  }

  return { pass: true, message: 'Frontend error handling implemented' };
}

// Test 5: Check red banner implementation
function testRedBanner() {
  const terminalPanelPath = path.join(__dirname, '../components/terminal/TerminalPanel.tsx');

  if (!fs.existsSync(terminalPanelPath)) {
    return { pass: false, message: 'TerminalPanel.tsx not found' };
  }

  const panelContent = fs.readFileSync(terminalPanelPath, 'utf8');

  const bannerFeatures = [
    'Task 86: PTY Error Banner',
    'border-l-4 border-red-500',
    'PTY Backend Failed',
    'node-pty is properly installed',
    'Retry PTY'
  ];

  const missingFeatures = bannerFeatures.filter(feature => !panelContent.includes(feature));

  if (missingFeatures.length > 0) {
    return { pass: false, message: `Missing red banner features: ${missingFeatures.join(', ')}` };
  }

  return { pass: true, message: 'Red banner UI implemented' };
}

// Test 6: Check status indicator enhancement
function testStatusIndicator() {
  const terminalPanelPath = path.join(__dirname, '../components/terminal/TerminalPanel.tsx');

  if (!fs.existsSync(terminalPanelPath)) {
    return { pass: false, message: 'TerminalPanel.tsx not found' };
  }

  const panelContent = fs.readFileSync(terminalPanelPath, 'utf8');

  const statusFeatures = [
    'ptyError ? \'bg-red-500\'',
    'bg-yellow-500',
    'connectionAttempts > 1',
    'Attempt {connectionAttempts}'
  ];

  const missingFeatures = statusFeatures.filter(feature => !panelContent.includes(feature));

  if (missingFeatures.length > 0) {
    return { pass: false, message: `Missing status indicator features: ${missingFeatures.join(', ')}` };
  }

  return { pass: true, message: 'Status indicator enhanced' };
}

// Run all tests
const allTests = [
  { name: 'Enhanced Logging', test: testEnhancedLogging },
  { name: 'Shell Fallback', test: testShellFallback },
  { name: 'PTY Error Handling', test: testPtyErrorHandling },
  { name: 'Frontend Error Handling', test: testFrontendErrorHandling },
  { name: 'Red Banner UI', test: testRedBanner },
  { name: 'Status Indicator', test: testStatusIndicator }
];

let allPassed = true;

allTests.forEach(({ name, test }) => {
  const result = test();
  const status = result.pass ? '✅' : '❌';
  console.log(`${status} ${name}: ${result.message}`);

  if (!result.pass) {
    allPassed = false;
  }
});

console.log('\n' + '='.repeat(60));
console.log(`Overall Status: ${allPassed ? '✅ PASS' : '❌ FAIL'}`);

if (allPassed) {
  console.log('\n🎉 Terminal startup debugging is fully implemented!');
  console.log('\n📋 Features implemented:');
  console.log('  ✅ Comprehensive PTY startup logging');
  console.log('  ✅ Auto-fallback shell detection');
  console.log('  ✅ Detailed error reporting with platform info');
  console.log('  ✅ Red banner UI for PTY failures');
  console.log('  ✅ Enhanced status indicators');
  console.log('  ✅ Retry mechanisms with attempt tracking');
  console.log('\n🧪 To test the implementation:');
  console.log('1. Run: npm run electron:dev');
  console.log('2. Open a terminal panel');
  console.log('3. Check console for detailed startup logs');
  console.log('4. If PTY fails, verify red banner appears');
  console.log('5. Test retry functionality');
} else {
  console.log('\n🔧 Please fix the issues above and run this script again.');
}

process.exit(allPassed ? 0 : 1);
