// services/active-project-service.ts
// Service for managing the currently active project

export interface ActiveProject {
  name: string;
  path: string;
  activatedAt: number;
  metadata?: {
    description?: string;
    version?: string;
    type?: string;
    lastModified?: number;
  };
}

class ActiveProjectService {
  private activeProject: ActiveProject | null = null;
  private listeners: Array<(project: ActiveProject | null) => void> = [];

  /**
   * Set the active project
   */
  setActiveProject(projectPath: string, projectName?: string): void {
    const name = projectName || this.extractProjectNameFromPath(projectPath);
    
    this.activeProject = {
      name,
      path: projectPath,
      activatedAt: Date.now(),
      metadata: {
        type: 'unknown',
        lastModified: Date.now()
      }
    };

    console.log(`✅ Active project set: ${name} (${projectPath})`);
    this.notifyListeners();
  }

  /**
   * Get the currently active project
   */
  getActiveProject(): ActiveProject | null {
    return this.activeProject;
  }

  /**
   * Clear the active project
   */
  clearActiveProject(): void {
    this.activeProject = null;
    console.log('✅ Active project cleared');
    this.notifyListeners();
  }

  /**
   * Check if a project is currently active
   */
  isProjectActive(projectPath: string): boolean {
    return this.activeProject?.path === projectPath;
  }

  /**
   * Get active project path
   */
  getActiveProjectPath(): string | null {
    return this.activeProject?.path || null;
  }

  /**
   * Get active project name
   */
  getActiveProjectName(): string | null {
    return this.activeProject?.name || null;
  }

  /**
   * Update active project metadata
   */
  updateActiveProjectMetadata(metadata: Partial<ActiveProject['metadata']>): void {
    if (this.activeProject) {
      this.activeProject.metadata = {
        ...this.activeProject.metadata,
        ...metadata,
        lastModified: Date.now()
      };
      this.notifyListeners();
    }
  }

  /**
   * Add listener for active project changes
   */
  addListener(listener: (project: ActiveProject | null) => void): void {
    this.listeners.push(listener);
  }

  /**
   * Remove listener for active project changes
   */
  removeListener(listener: (project: ActiveProject | null) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index >= 0) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Notify all listeners of active project changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.activeProject);
      } catch (error) {
        console.warn('Error in active project listener:', error);
      }
    });
  }

  /**
   * Extract project name from path
   */
  private extractProjectNameFromPath(projectPath: string): string {
    const parts = projectPath.split(/[/\\]/);
    return parts[parts.length - 1] || 'Unknown Project';
  }

  /**
   * Get project statistics
   */
  getProjectStats(): {
    hasActiveProject: boolean;
    activeDuration: number;
    projectName: string | null;
    projectPath: string | null;
  } {
    if (!this.activeProject) {
      return {
        hasActiveProject: false,
        activeDuration: 0,
        projectName: null,
        projectPath: null
      };
    }

    return {
      hasActiveProject: true,
      activeDuration: Date.now() - this.activeProject.activatedAt,
      projectName: this.activeProject.name,
      projectPath: this.activeProject.path
    };
  }

  /**
   * Initialize the service
   */
  initialize(): void {
    console.log('✅ Active project service initialized');
  }

  /**
   * Cleanup the service
   */
  cleanup(): void {
    this.activeProject = null;
    this.listeners = [];
    console.log('✅ Active project service cleaned up');
  }
}

// Export singleton instance
export const activeProjectService = new ActiveProjectService();
