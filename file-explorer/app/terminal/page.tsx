"use client"

import dynamic from 'next/dynamic';
import { ThemeProvider } from "next-themes";
import React from 'react';

// ✅ Fix SSR: Dynamically import TerminalPanel to avoid xterm.js SSR issues
const TerminalPanel = dynamic(() => import('@/components/terminal/TerminalPanel'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full text-muted-foreground">
      <div className="text-center">
        <div className="text-sm mb-1">Loading terminal...</div>
        <div className="text-xs text-muted-foreground/70">
          Initializing terminal components
        </div>
      </div>
    </div>
  )
});

export default function TerminalWindowPage() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="flex flex-col h-screen bg-background text-foreground">
        {/* ✅ Task 83: Replace TerminalManager with real xterm.js TerminalPanel */}
        <TerminalPanel
          shellType="bash"
          className="h-full"
        />
      </div>
    </ThemeProvider>
  );
}