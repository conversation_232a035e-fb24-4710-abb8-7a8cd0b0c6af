"use client"

import dynamic from 'next/dynamic';
import { ThemeProvider } from "next-themes";
import React from 'react';

// ✅ Fix SSR: Dynamically import TerminalPanel with error handling
const TerminalPanel = dynamic(() => import('@/components/terminal/TerminalPanel').catch(error => {
  console.error('❌ [TerminalPage] Failed to load TerminalPanel:', error);
  // Return a fallback component
  return {
    default: () => (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-center">
          <div className="text-sm mb-1 text-red-500">Terminal Component Error</div>
          <div className="text-xs text-muted-foreground/70 mb-2">
            Failed to load terminal component: {error.message}
          </div>
          <button
            onClick={() => window.location.reload()}
            className="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Reload
          </button>
        </div>
      </div>
    )
  };
}), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full text-muted-foreground">
      <div className="text-center">
        <div className="text-sm mb-1">Loading terminal...</div>
        <div className="text-xs text-muted-foreground/70">
          Initializing terminal components
        </div>
      </div>
    </div>
  )
});

export default function TerminalWindowPage() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="flex flex-col h-screen bg-background text-foreground">
        {/* ✅ Task 83: Replace TerminalManager with real xterm.js TerminalPanel */}
        <TerminalPanel
          shellType="bash"
          className="h-full"
        />
      </div>
    </ThemeProvider>
  );
}