/**
 * ✅ Taskmaster Integration Service
 *
 * Automatically initializes and configures Taskmaster CLI for each project,
 * inheriting API keys from application settings and ensuring seamless operation.
 */

import { settingsManager } from '../settings/settings-manager';
import { activeProjectService } from './active-project-service';

export interface TaskmasterInitResult {
  success: boolean;
  error?: string;
  configPath?: string;
  projectPath?: string;
}

export interface TaskmasterConfig {
  project: {
    name: string;
    description: string;
    version: string;
  };
  models: {
    main: string;
    research?: string;
    fallback?: string;
  };
  apiKeys: {
    [provider: string]: string;
  };
  settings: {
    maxTasks: number;
    complexityThreshold: number;
    autoExpand: boolean;
  };
}

export class TaskmasterIntegrationService {
  private static instance: TaskmasterIntegrationService;

  public static getInstance(): TaskmasterIntegrationService {
    if (!TaskmasterIntegrationService.instance) {
      TaskmasterIntegrationService.instance = new TaskmasterIntegrationService();
    }
    return TaskmasterIntegrationService.instance;
  }

  /**
   * ✅ Automatically initialize Taskmaster for a project
   */
  public async initializeForProject(projectPath: string, projectName: string): Promise<TaskmasterInitResult> {
    try {
      console.log(`🔧 TaskmasterIntegration: Initializing Taskmaster for project: ${projectName}`);

      // Step 1: Create .taskmaster directory
      const taskmasterDir = `${projectPath}/.taskmaster`;
      if (typeof window !== 'undefined' && window.electronAPI) {
        const dirResult = await window.electronAPI.createFile(`${taskmasterDir}/.gitkeep`, '');
        if (!dirResult.success) {
          return {
            success: false,
            error: `Failed to create .taskmaster directory: ${dirResult.error}`
          };
        }
      }

      // Step 2: Generate .taskmasterconfig
      const config = await this.generateTaskmasterConfig(projectName);
      const configPath = `${projectPath}/.taskmasterconfig`;

      if (typeof window !== 'undefined' && window.electronAPI) {
        const configResult = await window.electronAPI.saveFile(
          configPath,
          JSON.stringify(config, null, 2)
        );

        if (!configResult.success) {
          return {
            success: false,
            error: `Failed to create .taskmasterconfig: ${configResult.error}`
          };
        }
      }

      // Step 3: Initialize Taskmaster CLI in project directory
      const initResult = await this.runTaskmasterInit(projectPath, projectName);
      if (!initResult.success) {
        return {
          success: false,
          error: `Taskmaster init failed: ${initResult.error}`
        };
      }

      // Step 4: Configure models automatically
      const modelResult = await this.configureTaskmasterModels(projectPath, config);
      if (!modelResult.success) {
        console.warn(`⚠️ TaskmasterIntegration: Model configuration failed: ${modelResult.error}`);
        // Continue anyway - basic functionality will work
      }

      console.log(`✅ TaskmasterIntegration: Successfully initialized Taskmaster for ${projectName}`);

      return {
        success: true,
        configPath,
        projectPath
      };
    } catch (error) {
      console.error('❌ TaskmasterIntegration: Initialization failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * ✅ Generate Taskmaster configuration from application settings
   */
  private async generateTaskmasterConfig(projectName: string): Promise<TaskmasterConfig> {
    // Get API keys from application settings
    const apiKeys: { [provider: string]: string } = {};

    // Map application API keys to Taskmaster format
    const anthropicKey = settingsManager.getApiKey('anthropic');
    const openaiKey = settingsManager.getApiKey('openai');
    const openrouterKey = settingsManager.getApiKey('openrouter');

    if (anthropicKey) apiKeys['ANTHROPIC_API_KEY'] = anthropicKey;
    if (openaiKey) apiKeys['OPENAI_API_KEY'] = openaiKey;
    if (openrouterKey) apiKeys['OPENROUTER_API_KEY'] = openrouterKey;

    // Determine primary model based on available API keys
    let mainModel = 'claude-3-5-sonnet-20241022'; // Default
    if (anthropicKey) {
      mainModel = 'claude-3-5-sonnet-20241022';
    } else if (openaiKey) {
      mainModel = 'gpt-4o';
    } else if (openrouterKey) {
      mainModel = 'anthropic/claude-3-5-sonnet-20241022';
    }

    return {
      project: {
        name: projectName,
        description: `AI-managed project: ${projectName}`,
        version: '1.0.0'
      },
      models: {
        main: mainModel,
        research: openaiKey ? 'gpt-4o' : mainModel,
        fallback: openrouterKey ? 'anthropic/claude-3-5-sonnet-20241022' : undefined
      },
      apiKeys,
      settings: {
        maxTasks: 50,
        complexityThreshold: 5,
        autoExpand: false
      }
    };
  }

  /**
   * ✅ Run taskmaster init command
   */
  private async runTaskmasterInit(projectPath: string, projectName: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const command = `npx task-master init --name="${projectName}" --description="AI-managed project" -y`;
        const result = await window.electronAPI.executeCommand(command, projectPath);

        if (result.success) {
          console.log(`✅ TaskmasterIntegration: Taskmaster init completed for ${projectName}`);
          return { success: true };
        } else {
          return {
            success: false,
            error: result.error || 'Init command failed'
          };
        }
      }

      return {
        success: false,
        error: 'Electron API not available'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * ✅ Configure Taskmaster models automatically
   */
  private async configureTaskmasterModels(projectPath: string, config: TaskmasterConfig): Promise<{ success: boolean; error?: string }> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        // Set main model
        const setMainCommand = `npx task-master models --set-main "${config.models.main}"`;
        const mainResult = await window.electronAPI.executeCommand(setMainCommand, projectPath);

        if (!mainResult.success) {
          return {
            success: false,
            error: `Failed to set main model: ${mainResult.error}`
          };
        }

        // Set research model if different
        if (config.models.research && config.models.research !== config.models.main) {
          const setResearchCommand = `npx task-master models --set-research "${config.models.research}"`;
          await window.electronAPI.executeCommand(setResearchCommand, projectPath);
        }

        // Set fallback model if available
        if (config.models.fallback) {
          const setFallbackCommand = `npx task-master models --set-fallback "${config.models.fallback}"`;
          await window.electronAPI.executeCommand(setFallbackCommand, projectPath);
        }

        console.log(`✅ TaskmasterIntegration: Models configured for ${config.project.name}`);
        return { success: true };
      }

      return {
        success: false,
        error: 'Electron API not available'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * ✅ Check if project has Taskmaster initialized
   */
  public async isInitialized(projectPath?: string): Promise<boolean> {
    try {
      const targetPath = projectPath || activeProjectService.getActiveProject()?.path;
      if (!targetPath) return false;

      const configPath = `${targetPath}/.taskmasterconfig`;

      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.readFile(configPath);
        return result.success;
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * ✅ Ensure Taskmaster is ready for current project
   */
  public async ensureInitialized(): Promise<TaskmasterInitResult> {
    const currentProject = activeProjectService.getActiveProject();

    if (!currentProject) {
      return {
        success: false,
        error: 'No active project selected'
      };
    }

    const isInit = await this.isInitialized(currentProject.path);

    if (isInit) {
      return {
        success: true,
        projectPath: currentProject.path
      };
    }

    // Auto-initialize if not already done
    return await this.initializeForProject(currentProject.path, currentProject.name);
  }

  /**
   * ✅ Create environment file with API keys for Taskmaster
   */
  private async createEnvironmentFile(projectPath: string, apiKeys: { [key: string]: string }): Promise<boolean> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const envContent = Object.entries(apiKeys)
          .map(([key, value]) => `${key}=${value}`)
          .join('\n');

        const envPath = `${projectPath}/.env`;
        const result = await window.electronAPI.saveFile(envPath, envContent);

        if (result.success) {
          console.log(`✅ TaskmasterIntegration: Created .env file with API keys`);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('❌ TaskmasterIntegration: Failed to create .env file:', error);
      return false;
    }
  }
}

// Export singleton instance
export const taskmasterIntegrationService = TaskmasterIntegrationService.getInstance();
