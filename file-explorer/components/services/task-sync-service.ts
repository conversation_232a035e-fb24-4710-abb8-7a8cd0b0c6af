// components/services/task-sync-service.ts
import { AgentExecutionResult } from '../agents/agent-base';
import { Card } from '../kanban/board-context';
import { taskmasterAdapter, AgentTask } from '../adapters/taskmaster-adapter';
import { activeProjectService } from './active-project-service';

export interface TaskSyncOptions {
  createBackup?: boolean;
  validateBeforeWrite?: boolean;
  retryOnFailure?: boolean;
  maxRetries?: number;
}

export interface TaskSyncResult {
  success: boolean;
  tasksUpdated: number;
  backupCreated?: string;
  error?: string;
  validationErrors?: string[];
}

export interface TaskUpdateFields {
  status?: 'pending' | 'running' | 'completed' | 'failed' | 'retrying';
  assignedAgent?: string;
  completionSummary?: string;
  outputs?: string[];
  progress?: number;
  lastUpdated?: string;
  executionMetrics?: {
    tokensUsed?: number;
    executionTime?: number;
    retryCount?: number;
  };
  kanbanCardId?: string;
}

/**
 * ✅ Task 78: Taskmaster Task Status Sync Service
 *
 * Synchronizes Kanban card status and metadata with the original tasks.json file
 * generated by Taskmaster AI to maintain consistency across systems.
 */
export class TaskSyncService {
  private static instance: TaskSyncService;

  private readonly DEFAULT_SYNC_OPTIONS: TaskSyncOptions = {
    createBackup: true,
    validateBeforeWrite: true,
    retryOnFailure: true,
    maxRetries: 3
  };

  public static getInstance(): TaskSyncService {
    if (!TaskSyncService.instance) {
      TaskSyncService.instance = new TaskSyncService();
    }
    return TaskSyncService.instance;
  }

  /**
   * Update Taskmaster task based on Kanban card changes
   */
  public async updateTaskmasterTask(
    card: Card,
    updateFields: TaskUpdateFields,
    options: TaskSyncOptions = {}
  ): Promise<TaskSyncResult> {
    const syncOptions = { ...this.DEFAULT_SYNC_OPTIONS, ...options };

    try {
      console.log(`🔄 TaskSyncService: Updating Taskmaster task for card ${card.id}`, updateFields);

      // Load current tasks from Taskmaster file
      const tasksResult = await taskmasterAdapter.loadTasks();
      if (!tasksResult.success || !tasksResult.data) {
        return {
          success: false,
          tasksUpdated: 0,
          error: `Failed to load Taskmaster tasks: ${tasksResult.error}`
        };
      }

      // Find matching task by card metadata
      const taskId = this.extractTaskIdFromCard(card);
      if (!taskId) {
        return {
          success: false,
          tasksUpdated: 0,
          error: 'No linked task ID found in card metadata'
        };
      }

      const taskIndex = tasksResult.data.tasks.findIndex(task => task.id === taskId);
      if (taskIndex === -1) {
        return {
          success: false,
          tasksUpdated: 0,
          error: `Task with ID ${taskId} not found in Taskmaster data`
        };
      }

      // Create backup if requested
      let backupPath: string | undefined;
      if (syncOptions.createBackup) {
        backupPath = await this.createTasksBackup();
      }

      // Update task with new fields
      const updatedTask = this.mergeTaskUpdate(tasksResult.data.tasks[taskIndex], updateFields);

      // Validate updated task if requested
      if (syncOptions.validateBeforeWrite) {
        const validationErrors = this.validateTaskUpdate(updatedTask);
        if (validationErrors.length > 0) {
          return {
            success: false,
            tasksUpdated: 0,
            error: 'Task validation failed',
            validationErrors
          };
        }
      }

      // Update task in data structure
      tasksResult.data.tasks[taskIndex] = updatedTask;

      // Write updated tasks back to file
      const writeResult = await this.writeTasksToFile(tasksResult.data, syncOptions);

      if (writeResult.success) {
        console.log(`✅ TaskSyncService: Successfully updated task ${taskId} in Taskmaster file`);
        return {
          success: true,
          tasksUpdated: 1,
          backupCreated: backupPath
        };
      } else {
        return {
          success: false,
          tasksUpdated: 0,
          error: writeResult.error,
          backupCreated: backupPath
        };
      }

    } catch (error) {
      console.error(`❌ TaskSyncService: Error updating Taskmaster task for card ${card.id}:`, error);
      return {
        success: false,
        tasksUpdated: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Sync agent execution result to Taskmaster task
   */
  public async syncExecutionResult(
    cardId: string,
    agentId: string,
    result: AgentExecutionResult,
    options: TaskSyncOptions = {}
  ): Promise<TaskSyncResult> {
    try {
      // Get card information (this would need to be implemented based on board system)
      const card = await this.getCardById(cardId);
      if (!card) {
        return {
          success: false,
          tasksUpdated: 0,
          error: `Card ${cardId} not found`
        };
      }

      // Convert execution result to task update fields
      const updateFields: TaskUpdateFields = {
        status: this.mapExecutionStatusToTaskStatus(result.status),
        assignedAgent: agentId,
        completionSummary: result.message,
        outputs: result.outputs,
        progress: this.calculateProgressFromResult(result),
        lastUpdated: new Date().toISOString(),
        executionMetrics: {
          tokensUsed: result.metrics?.tokensUsed,
          executionTime: result.metrics?.executionTime
        },
        kanbanCardId: cardId
      };

      return await this.updateTaskmasterTask(card, updateFields, options);

    } catch (error) {
      console.error(`❌ TaskSyncService: Error syncing execution result for card ${cardId}:`, error);
      return {
        success: false,
        tasksUpdated: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Bulk sync multiple card updates
   */
  public async bulkSyncTasks(
    updates: Array<{ card: Card; updateFields: TaskUpdateFields }>,
    options: TaskSyncOptions = {}
  ): Promise<TaskSyncResult> {
    const syncOptions = { ...this.DEFAULT_SYNC_OPTIONS, ...options };

    try {
      console.log(`🔄 TaskSyncService: Bulk syncing ${updates.length} task updates`);

      // Load current tasks
      const tasksResult = await taskmasterAdapter.loadTasks();
      if (!tasksResult.success || !tasksResult.data) {
        return {
          success: false,
          tasksUpdated: 0,
          error: `Failed to load Taskmaster tasks: ${tasksResult.error}`
        };
      }

      // Create backup if requested
      let backupPath: string | undefined;
      if (syncOptions.createBackup) {
        backupPath = await this.createTasksBackup();
      }

      let updatedCount = 0;
      const validationErrors: string[] = [];

      // Process each update
      for (const { card, updateFields } of updates) {
        const taskId = this.extractTaskIdFromCard(card);
        if (!taskId) {
          validationErrors.push(`Card ${card.id}: No linked task ID found`);
          continue;
        }

        const taskIndex = tasksResult.data.tasks.findIndex(task => task.id === taskId);
        if (taskIndex === -1) {
          validationErrors.push(`Card ${card.id}: Task ${taskId} not found`);
          continue;
        }

        // Update task
        const updatedTask = this.mergeTaskUpdate(tasksResult.data.tasks[taskIndex], updateFields);

        // Validate if requested
        if (syncOptions.validateBeforeWrite) {
          const taskValidationErrors = this.validateTaskUpdate(updatedTask);
          if (taskValidationErrors.length > 0) {
            validationErrors.push(`Task ${taskId}: ${taskValidationErrors.join(', ')}`);
            continue;
          }
        }

        tasksResult.data.tasks[taskIndex] = updatedTask;
        updatedCount++;
      }

      // Write updated tasks back to file
      const writeResult = await this.writeTasksToFile(tasksResult.data, syncOptions);

      if (writeResult.success) {
        console.log(`✅ TaskSyncService: Successfully bulk updated ${updatedCount} tasks`);
        return {
          success: true,
          tasksUpdated: updatedCount,
          backupCreated: backupPath,
          validationErrors: validationErrors.length > 0 ? validationErrors : undefined
        };
      } else {
        return {
          success: false,
          tasksUpdated: 0,
          error: writeResult.error,
          backupCreated: backupPath,
          validationErrors
        };
      }

    } catch (error) {
      console.error(`❌ TaskSyncService: Error in bulk sync:`, error);
      return {
        success: false,
        tasksUpdated: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Extract task ID from card metadata
   */
  private extractTaskIdFromCard(card: Card): string | null {
    // Check various possible locations for task ID
    const metadata = card as any;

    return metadata.linkedTaskId ||
           metadata.taskId ||
           metadata.originalTaskId ||
           metadata.projectId ||
           null;
  }

  /**
   * Merge task update fields with existing task
   */
  private mergeTaskUpdate(existingTask: AgentTask, updateFields: TaskUpdateFields): AgentTask {
    const updatedTask = { ...existingTask };

    // Map update fields to task properties
    if (updateFields.status !== undefined) {
      (updatedTask as any).status = updateFields.status;
    }

    if (updateFields.assignedAgent !== undefined) {
      updatedTask.assignedAgentId = updateFields.assignedAgent;
    }

    if (updateFields.completionSummary !== undefined) {
      (updatedTask as any).completionSummary = updateFields.completionSummary;
    }

    if (updateFields.outputs !== undefined) {
      (updatedTask as any).outputs = updateFields.outputs;
    }

    if (updateFields.progress !== undefined) {
      (updatedTask as any).progress = updateFields.progress;
    }

    if (updateFields.lastUpdated !== undefined) {
      (updatedTask as any).lastUpdated = updateFields.lastUpdated;
    }

    if (updateFields.executionMetrics !== undefined) {
      (updatedTask as any).executionMetrics = updateFields.executionMetrics;
    }

    if (updateFields.kanbanCardId !== undefined) {
      (updatedTask as any).kanbanCardId = updateFields.kanbanCardId;
    }

    return updatedTask;
  }

  /**
   * Validate task update
   */
  private validateTaskUpdate(task: AgentTask): string[] {
    const errors: string[] = [];

    if (!task.id) {
      errors.push('Task ID is required');
    }

    if (!task.title) {
      errors.push('Task title is required');
    }

    if (!task.assignedAgentId) {
      errors.push('Assigned agent ID is required');
    }

    // Validate status if present
    const validStatuses = ['pending', 'running', 'completed', 'failed', 'retrying'];
    const taskStatus = (task as any).status;
    if (taskStatus && !validStatuses.includes(taskStatus)) {
      errors.push(`Invalid status: ${taskStatus}`);
    }

    return errors;
  }

  /**
   * Map execution status to task status
   */
  private mapExecutionStatusToTaskStatus(executionStatus: string): TaskUpdateFields['status'] {
    switch (executionStatus) {
      case 'success':
        return 'completed';
      case 'error':
      case 'timeout':
        return 'failed';
      case 'partial':
        return 'running';
      default:
        return 'pending';
    }
  }

  /**
   * Calculate progress from execution result
   */
  private calculateProgressFromResult(result: AgentExecutionResult): number {
    switch (result.status) {
      case 'success':
        return 100;
      case 'error':
      case 'timeout':
        return 0;
      case 'partial':
        // Calculate based on outputs and files
        let progress = 50; // Base for partial
        if (result.outputs?.length) progress += 20;
        if (result.createdFiles?.length || result.modifiedFiles?.length) progress += 20;
        return Math.min(progress, 90);
      default:
        return 0;
    }
  }

  /**
   * Create backup of tasks file
   */
  private async createTasksBackup(): Promise<string | undefined> {
    try {
      const currentProject = activeProjectService.getActiveProject();
      if (!currentProject) {
        console.warn('⚠️ TaskSyncService: No active project for backup');
        return undefined;
      }

      const tasksFilePath = `${currentProject.path}/.taskmaster/tasks.json`;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = `${currentProject.path}/.taskmaster/tasks.backup.${timestamp}.json`;

      if (typeof window !== 'undefined' && window.electronAPI) {
        // Read original file
        const originalFile = await window.electronAPI.readFile(tasksFilePath);
        if (originalFile.success) {
          // Write backup
          const backupResult = await window.electronAPI.saveFile(backupPath, originalFile.content);
          if (backupResult.success) {
            console.log(`📁 TaskSyncService: Created backup at ${backupPath}`);
            return backupPath;
          }
        }
      }

      return undefined;
    } catch (error) {
      console.error(`❌ TaskSyncService: Failed to create backup:`, error);
      return undefined;
    }
  }

  /**
   * Write tasks data to file
   */
  private async writeTasksToFile(
    tasksData: any,
    options: TaskSyncOptions
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const currentProject = activeProjectService.getActiveProject();
      if (!currentProject) {
        return { success: false, error: 'No active project' };
      }

      const tasksFilePath = `${currentProject.path}/.taskmaster/tasks.json`;
      const jsonContent = JSON.stringify(tasksData, null, 2);

      if (typeof window !== 'undefined' && window.electronAPI) {
        let retryCount = 0;
        const maxRetries = options.maxRetries || 3;

        while (retryCount <= maxRetries) {
          try {
            const result = await window.electronAPI.saveFile(tasksFilePath, jsonContent);
            if (result.success) {
              return { success: true };
            } else {
              throw new Error(result.error || 'Failed to save file');
            }
          } catch (error) {
            retryCount++;
            if (retryCount > maxRetries || !options.retryOnFailure) {
              throw error;
            }
            console.warn(`⚠️ TaskSyncService: Retry ${retryCount}/${maxRetries} for file write`);
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // Exponential backoff
          }
        }
      }

      return { success: false, error: 'Electron API not available' };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get card by ID (placeholder - would need actual implementation)
   */
  private async getCardById(cardId: string): Promise<Card | null> {
    // This would need to be implemented based on the board system
    // For now, return null to indicate not found
    console.warn(`⚠️ TaskSyncService: getCardById not implemented for ${cardId}`);
    return null;
  }
}

// Export singleton instance
export const taskSyncService = TaskSyncService.getInstance();
